#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sukebei磁力链接获取脚本
参考av项目的实现方式，从Sukebei搜索并获取番号对应的磁力链接
"""

import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import quote, urljoin
from dataclasses import dataclass, field
from typing import List, Optional, Dict
import json

# 硬编码配置的番号
TARGET_CODE = "FSDSS-351"

# 硬编码配置：最小文件大小阈值（GB）
MIN_SIZE_GB = 3.0

# 硬编码配置：文件名中必须包含的标识符
REQUIRED_IDENTIFIER = "hhd800.com@"


@dataclass
class MagnetInfo:
    """磁力链接信息结构体，对应Rust项目中的MagnetInfo"""
    url: str
    name: Optional[str] = None
    size: Optional[str] = None
    date: Optional[str] = None
    seeders: Optional[int] = None
    leechers: Optional[int] = None
    downloads: Optional[int] = None
    resolution: Optional[str] = None
    codec: Optional[str] = None
    avg_bitrate_mbps: Optional[float] = None
    file_list: Optional[List[Dict[str, str]]] = field(default_factory=list)
    submitter: Optional[str] = None
    completed: Optional[int] = None


def parse_size_to_gb(size_str: str) -> float:
    """
    将文件大小字符串转换为GB数值
    支持格式：1.2 GiB, 500 MiB, 2.5 GB, 1024 MB等
    """
    if not size_str:
        return 0.0

    # 清理字符串，移除多余空白
    size_str = size_str.strip().upper()

    # 提取数字和单位
    import re
    match = re.match(r'(\d+\.?\d*)\s*([KMGT]?I?B)', size_str)
    if not match:
        return 0.0

    value = float(match.group(1))
    unit = match.group(2)

    # 转换为GB
    if unit in ['B', 'BYTES']:
        return value / (1024 ** 3)
    elif unit in ['KB', 'KIB']:
        return value / (1024 ** 2)
    elif unit in ['MB', 'MIB']:
        return value / 1024
    elif unit in ['GB', 'GIB']:
        return value
    elif unit in ['TB', 'TIB']:
        return value * 1024
    else:
        return 0.0


class SukebeiScraper:
    """Sukebei爬虫类，参考av项目的实现"""

    def __init__(self):
        # 模拟浏览器的User-Agent，与原项目保持一致
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def extract_code_from_title(self, title: str) -> Optional[str]:
        """从标题中提取番号，对应Rust项目中的extract_code_from_title函数"""
        # 正则表达式与原项目保持一致: r"(?i)([a-z]{2,5})[-_ ]?(\d{2,5})"
        pattern = r"(?i)([a-z]{2,5})[-_ ]?(\d{2,5})"
        match = re.search(pattern, title)
        if match:
            return f"{match.group(1).upper()}-{match.group(2)}"
        return None

    def extract_magnets_from_text(self, text: str) -> List[str]:
        """从文本中提取磁力链接，对应Rust项目中的extract_magnets_from_text函数"""
        # 正则表达式与原项目保持一致: r#"magnet:\?xt=urn:[^"'\s<>]+"#
        pattern = r'magnet:\?xt=urn:[^"\'\s<>]+'
        return re.findall(pattern, text)

    def parse_resolution_and_codec(self, title: str) -> tuple[Optional[str], Optional[str]]:
        """从标题中解析分辨率和编码格式"""
        # 分辨率正则: r"(\d{3,4}p|\d{3,4}x\d{3,4})"
        res_pattern = r"(\d{3,4}p|\d{3,4}x\d{3,4})"
        res_match = re.search(res_pattern, title)
        resolution = res_match.group(1) if res_match else None

        # 编码格式正则: r"(H\.264|H\.265|AVC|HEVC|x264|x265)"
        codec_pattern = r"(H\.264|H\.265|AVC|HEVC|x264|x265)"
        codec_match = re.search(codec_pattern, title, re.IGNORECASE)
        codec = codec_match.group(1) if codec_match else None

        return resolution, codec

    def parse_file_list(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """
        从Sukebei详情页解析文件列表
        基于观察到的HTML结构进行优化解析
        """
        file_list = []

        try:
            # 文件大小模式
            size_pattern = r'(\d+\.?\d*\s*(?:Bytes?|KiB|MiB|GiB|TiB))'

            # 方法1: 查找"File list"标题附近的内容
            # 寻找包含"File list"文本的元素
            file_list_headers = soup.find_all(string=re.compile(r'file\s*list', re.IGNORECASE))

            for header in file_list_headers:
                # 找到"File list"标题后的兄弟元素或父元素的后续内容
                parent = header.parent
                if parent:
                    # 查找父元素后面的所有兄弟元素
                    next_siblings = []
                    current = parent.next_sibling
                    sibling_count = 0
                    while current and sibling_count < 10:  # 限制搜索范围
                        if hasattr(current, 'get_text'):
                            next_siblings.append(current)
                        current = current.next_sibling
                        sibling_count += 1

                    # 解析兄弟元素中的文件信息
                    for sibling in next_siblings:
                        sibling_text = sibling.get_text()
                        if sibling_text.strip():
                            parsed_files = self._parse_text_for_files(sibling_text, size_pattern)
                            file_list.extend(parsed_files)

                    # 如果找到了文件，就停止搜索其他"File list"标题
                    if file_list:
                        break

            # 方法2: 如果方法1没找到，查找整个页面中的文件信息
            if not file_list:
                # 获取页面的主要内容区域
                main_content = soup.find('body')
                if main_content:
                    # 查找所有包含文件大小模式的文本
                    all_text = main_content.get_text()
                    file_list = self._parse_text_for_files(all_text, size_pattern)

            # 方法3: 特定于Sukebei的结构解析
            if not file_list:
                # 查找可能包含文件列表的特定元素
                potential_containers = soup.find_all(['div', 'pre', 'code', 'section'])

                for container in potential_containers:
                    container_text = container.get_text()

                    # 检查容器是否包含多个文件大小信息（表明这是文件列表）
                    size_matches = re.findall(size_pattern, container_text, re.IGNORECASE)
                    if len(size_matches) >= 1:  # 降低阈值，至少包含1个文件大小信息
                        container_files = self._parse_text_for_files(container_text, size_pattern)
                        if len(container_files) > len(file_list):
                            file_list = container_files

            if file_list:
                print(f"解析到 {len(file_list)} 个文件")

        except Exception as e:
            print(f"解析文件列表时出错: {e}")
            import traceback
            traceback.print_exc()

        return file_list

    def _parse_text_for_files(self, text: str, size_pattern: str) -> List[Dict[str, str]]:
        """
        从文本中解析文件信息的辅助方法
        """
        file_list = []
        lines = text.split('\n')
        current_folder = ""

        for line in lines:
            line = line.strip()
            if not line or len(line) < 3:
                continue

            # 跳过明显不是文件的行
            skip_words = [
                'file list', 'download', 'magnet', 'torrent', 'http://', 'https://',
                'comments', 'seeders', 'leechers', 'date:', 'size:', 'category:',
                'submitter:', 'information:', 'info hash:'
            ]

            should_skip = any(skip_word in line.lower() for skip_word in skip_words)
            if should_skip:
                continue

            # 检查是否包含文件大小
            size_match = re.search(size_pattern, line, re.IGNORECASE)

            if size_match:
                # 这是一个文件行
                size = size_match.group(1).strip()
                filename = re.sub(size_pattern, '', line, flags=re.IGNORECASE).strip()

                # 清理文件名
                filename = re.sub(r'\s+', ' ', filename)
                filename = filename.strip('()[]{}.,;:')

                # 过滤掉太短或包含特殊字符的文件名
                if len(filename) > 2 and not any(char in filename for char in ['<', '>', '|', '*', '?']):
                    file_info = {
                        'name': filename,
                        'size': size,
                        'type': 'file',
                        'folder': current_folder
                    }

                    # 避免重复添加相同的文件
                    if not any(f['name'] == filename and f['size'] == size for f in file_list):
                        file_list.append(file_info)

            else:
                # 可能是文件夹名
                # 文件夹名通常较短，不包含特殊字符，且不包含文件扩展名
                if (len(line) < 80 and
                    not re.search(r'\.[a-zA-Z0-9]{2,4}$', line) and  # 不以文件扩展名结尾
                    not any(char in line for char in ['http', '://', '@', '#']) and
                    len(line.replace(' ', '').replace('-', '').replace('_', '')) > 0):
                    current_folder = line
        return file_list

    def parse_submitter_and_completed(self, soup: BeautifulSoup) -> tuple[Optional[str], Optional[int]]:
        """
        从Sukebei详情页解析Submitter和Completed信息
        """
        submitter = None
        completed = None

        try:
            # 获取页面的所有文本内容
            page_text = soup.get_text()

            # 方法1: 查找包含"Submitter:"的文本
            submitter_match = re.search(r'Submitter:\s*([^\n\r]+)', page_text, re.IGNORECASE)
            if submitter_match:
                submitter = submitter_match.group(1).strip()

            # 方法2: 查找包含"Completed:"的文本
            completed_match = re.search(r'Completed:\s*(\d+)', page_text, re.IGNORECASE)
            if completed_match:
                completed = int(completed_match.group(1))

            # 方法3: 如果上述方法没找到，尝试从HTML结构中查找
            if not submitter or completed is None:
                # 查找所有可能包含这些信息的元素
                info_elements = soup.find_all(['div', 'span', 'td', 'p'])

                for element in info_elements:
                    element_text = element.get_text().strip()

                    # 查找Submitter信息
                    if not submitter and 'submitter' in element_text.lower():
                        # 尝试提取submitter名称
                        submitter_patterns = [
                            r'submitter[:\s]*([^\n\r\t]+)',
                            r'提交者[:\s]*([^\n\r\t]+)',
                            r'上传者[:\s]*([^\n\r\t]+)'
                        ]

                        for pattern in submitter_patterns:
                            match = re.search(pattern, element_text, re.IGNORECASE)
                            if match:
                                submitter = match.group(1).strip()
                                # 清理submitter名称，移除多余的空白和特殊字符
                                submitter = re.sub(r'\s+', ' ', submitter)
                                submitter = submitter.strip('.,;:')
                                if len(submitter) > 0 and len(submitter) < 50:  # 合理的长度范围
                                    break

                    # 查找Completed信息
                    if completed is None and 'completed' in element_text.lower():
                        # 尝试提取completed数字
                        completed_patterns = [
                            r'completed[:\s]*(\d+)',
                            r'完成[:\s]*(\d+)',
                            r'下载完成[:\s]*(\d+)'
                        ]

                        for pattern in completed_patterns:
                            match = re.search(pattern, element_text, re.IGNORECASE)
                            if match:
                                completed = int(match.group(1))
                                break

            # 验证和清理结果
            if submitter:
                # 进一步清理submitter名称
                submitter = submitter.strip()
                # 如果submitter包含明显的非用户名内容，则置为None
                if any(word in submitter.lower() for word in ['http', 'www', '.com', '.org', 'magnet:', 'torrent']):
                    submitter = None
                elif len(submitter) > 100:  # 太长的可能不是用户名
                    submitter = None

            if completed is not None:
                # 确保completed是合理的数字
                if completed < 0 or completed > 1000000:  # 设置合理的范围
                    completed = None



        except Exception as e:
            print(f"解析Submitter和Completed时出错: {e}")

        return submitter, completed

    def check_file_list_contains_identifier(self, file_list: List[Dict[str, str]], identifier: str) -> bool:
        """
        检查文件列表中是否包含指定的标识符
        """
        if not file_list:
            return False

        for file_info in file_list:
            file_name = file_info.get('name', '')
            if identifier in file_name:
                return True

        return False

    def search_sukebei(self, code: str) -> Optional[MagnetInfo]:
        """
        在Sukebei搜索番号，使用新的选择逻辑：
        1. 收集所有匹配的种子
        2. 筛选大于指定大小的种子
        3. 按时间从老到新排序
        4. 依次检查文件列表是否包含指定标识符
        """
        try:
            # 构建搜索URL
            search_url = f"https://sukebei.nyaa.si/?f=0&c=0_0&q={quote(code)}"
            print(f"搜索URL: {search_url}")

            # 发送搜索请求
            response = self.session.get(search_url)
            response.raise_for_status()

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找种子列表表格
            torrent_rows = soup.select('table.torrent-list tbody tr')

            # 第一步：收集所有匹配番号的种子信息
            candidate_torrents = []

            for row in torrent_rows:
                # 查找标题链接
                title_links = row.select('td[colspan] a, td:nth-child(2) a')
                if title_links:
                    title_element = title_links[0]
                    title = title_element.get_text().strip()

                    # 检查标题是否包含目标番号
                    if code.upper() in title.upper():
                        href = title_element.get('href')
                        if href:
                            # 提取基本信息
                            magnet_info = self.extract_info_from_row(row, code)
                            if magnet_info:
                                detail_url = urljoin("https://sukebei.nyaa.si", href)
                                candidate_torrents.append({
                                    'magnet_info': magnet_info,
                                    'detail_url': detail_url,
                                    'title': title
                                })
                                print(f"找到候选项: {title}")

            if not candidate_torrents:
                print(f"未在Sukebei找到番号: {code}")
                return None

            print(f"共找到 {len(candidate_torrents)} 个候选种子")

            # 第二步：筛选大于指定大小的种子
            large_torrents = []
            for candidate in candidate_torrents:
                magnet_info = candidate['magnet_info']
                if magnet_info.size:
                    size_gb = parse_size_to_gb(magnet_info.size)
                    if size_gb >= MIN_SIZE_GB:
                        candidate['size_gb'] = size_gb
                        large_torrents.append(candidate)
                        print(f"符合大小要求: {candidate['title']} ({magnet_info.size}, {size_gb:.2f}GB)")

            if not large_torrents:
                print(f"没有找到大于 {MIN_SIZE_GB}GB 的种子")
                return None

            print(f"筛选出 {len(large_torrents)} 个大于 {MIN_SIZE_GB}GB 的种子")

            # 第三步：按日期排序（从老到新）
            # 先按日期字符串排序，如果没有日期则放在最前面
            large_torrents.sort(key=lambda x: x['magnet_info'].date or "0000-00-00")

            print("按时间排序后的候选列表（从老到新）：")
            for i, candidate in enumerate(large_torrents):
                magnet_info = candidate['magnet_info']
                print(f"  {i+1}. {candidate['title']} - {magnet_info.date} ({magnet_info.size})")

            # 第四步：依次检查文件列表是否包含指定标识符
            print(f"\n开始检查文件列表是否包含标识符: {REQUIRED_IDENTIFIER}")

            for i, candidate in enumerate(large_torrents):
                print(f"\n检查第 {i+1} 个种子: {candidate['title']}")

                # 获取详情页信息
                detail_magnets = self.parse_sukebei_detail(candidate['detail_url'], code)

                if detail_magnets:
                    detail_info = detail_magnets[0]

                    # 检查文件列表是否包含指定标识符
                    if self.check_file_list_contains_identifier(detail_info.file_list, REQUIRED_IDENTIFIER):
                        print(f"✅ 找到包含标识符的种子: {candidate['title']}")

                        # 合并信息
                        magnet_info = candidate['magnet_info']
                        magnet_info.file_list = detail_info.file_list
                        magnet_info.submitter = detail_info.submitter
                        magnet_info.completed = detail_info.completed

                        return magnet_info
                    else:
                        print(f"❌ 该种子文件列表中不包含标识符")
                else:
                    print(f"❌ 无法获取该种子的详情信息")

            print(f"\n所有符合大小要求的种子都不包含标识符 '{REQUIRED_IDENTIFIER}'")
            return None

        except Exception as e:
            print(f"搜索Sukebei时出错: {e}")
            return None

    def extract_info_from_row(self, row, code: str) -> Optional[MagnetInfo]:
        """从搜索结果行中提取磁力链接信息"""
        try:
            # 获取所有td元素
            tds = row.select('td')
            if len(tds) < 8:
                return None

            # 查找磁力链接
            magnet_links = row.select('a[href^="magnet:"]')
            magnet_url = magnet_links[0].get('href') if magnet_links else None

            # 提取各种信息，对应原项目中的索引位置
            # tds[3] = 文件大小, tds[4] = 日期, tds[5] = 种子数, tds[6] = 下载者数, tds[7] = 完成数
            size = tds[3].get_text().strip() if len(tds) > 3 else None
            date = tds[4].get_text().strip() if len(tds) > 4 else None

            # 解析数字字段
            try:
                seeders = int(tds[5].get_text().strip()) if len(tds) > 5 and tds[
                    5].get_text().strip().isdigit() else None
            except (ValueError, IndexError):
                seeders = None

            try:
                leechers = int(tds[6].get_text().strip()) if len(tds) > 6 and tds[
                    6].get_text().strip().isdigit() else None
            except (ValueError, IndexError):
                leechers = None

            try:
                downloads = int(tds[7].get_text().strip()) if len(tds) > 7 and tds[
                    7].get_text().strip().isdigit() else None
            except (ValueError, IndexError):
                downloads = None

            # 获取标题用于解析分辨率和编码
            title_element = row.select('td[colspan] a, td:nth-child(2) a')
            title = title_element[0].get_text().strip() if title_element else ""
            resolution, codec = self.parse_resolution_and_codec(title)

            return MagnetInfo(
                url=magnet_url or "",
                name=title if title else None,
                size=size,
                date=date,
                seeders=seeders,
                leechers=leechers,
                downloads=downloads,
                resolution=resolution,
                codec=codec,
                file_list=[],  # 搜索结果行中没有文件列表，在详情页中获取
                submitter=None,  # 搜索结果行中没有submitter信息，在详情页中获取
                completed=None   # 搜索结果行中没有completed信息，在详情页中获取
            )

        except Exception as e:
            print(f"解析行信息时出错: {e}")
            return None

    def parse_sukebei_detail(self, url: str, code: str) -> List[MagnetInfo]:
        """
        解析Sukebei详情页，对应Rust项目中的parse_sukebei_detail函数
        """
        try:
            print(f"获取详情页: {url}")
            response = self.session.get(url)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 获取种子名称，对应原项目的.torrent-name选择器
            title_element = soup.select_one('.torrent-name')
            title = title_element.get_text().strip() if title_element else code

            # 提取所有磁力链接，对应原项目的a[href^='magnet:']选择器
            magnet_links = soup.select('a[href^="magnet:"]')
            magnets = [link.get('href') for link in magnet_links if link.get('href')]

            # 解析分辨率和编码
            resolution, codec = self.parse_resolution_and_codec(title)

            # 解析文件列表
            file_list = self.parse_file_list(soup)

            # 解析Submitter和Completed信息
            submitter, completed = self.parse_submitter_and_completed(soup)

            # 构建MagnetInfo列表
            magnet_infos = []
            for magnet in magnets:
                magnet_infos.append(MagnetInfo(
                    url=magnet,
                    name=title,
                    resolution=resolution,
                    codec=codec,
                    file_list=file_list,
                    submitter=submitter,
                    completed=completed
                ))

            return magnet_infos

        except Exception as e:
            print(f"解析详情页时出错: {e}")
            return []


def main():
    """主函数"""
    print(f"开始搜索番号: {TARGET_CODE}")
    print("=" * 50)

    # 创建爬虫实例
    scraper = SukebeiScraper()

    # 搜索磁力链接
    magnet_info = scraper.search_sukebei(TARGET_CODE)

    if magnet_info and magnet_info.url:
        print(f"\n✅ 找到磁力链接!")
        print(f"番号: {TARGET_CODE}")
        print(f"标题: {magnet_info.name or '未知'}")
        print(f"磁力链接: {magnet_info.url}")

        # 显示详细信息
        details = []
        if magnet_info.size:
            details.append(f"大小: {magnet_info.size}")
        if magnet_info.resolution:
            details.append(f"分辨率: {magnet_info.resolution}")
        if magnet_info.codec:
            details.append(f"编码: {magnet_info.codec}")
        if magnet_info.seeders is not None:
            details.append(f"种子数: {magnet_info.seeders}")
        if magnet_info.leechers is not None:
            details.append(f"下载者: {magnet_info.leechers}")
        if magnet_info.downloads is not None:
            details.append(f"完成数: {magnet_info.downloads}")
        if magnet_info.submitter:
            details.append(f"提交者: {magnet_info.submitter}")
        if magnet_info.completed is not None:
            details.append(f"下载完成: {magnet_info.completed}")

        if details:
            print(f"详细信息: {' | '.join(details)}")

        # 显示文件列表
        if magnet_info.file_list and len(magnet_info.file_list) > 0:
            print(f"\n📁 文件列表 ({len(magnet_info.file_list)} 个文件):")
            print("-" * 80)

            # 按文件夹分组显示
            folders = {}
            for file_info in magnet_info.file_list:
                folder = file_info.get('folder', '')
                if folder not in folders:
                    folders[folder] = []
                folders[folder].append(file_info)

            for folder, files in folders.items():
                if folder:
                    print(f"\n� {folder}/")
                    for file_info in files:
                        print(f"  📄 {file_info['name']} ({file_info['size']})")
                else:
                    # 根目录文件
                    for file_info in files:
                        print(f"📄 {file_info['name']} ({file_info['size']})")

            print("-" * 80)
        else:
            print(f"\n📁 文件列表: 未获取到文件列表信息")

        print(f"\n�📋 使用方法:")
        print(f"- 复制磁力链接到您的BT客户端")
        print(f"- 或使用命令行: aria2c \"{magnet_info.url}\"")

        # 可选：输出JSON格式
        print(f"\n🔧 JSON格式输出:")
        magnet_dict = {
            'code': TARGET_CODE,
            'url': magnet_info.url,
            'name': magnet_info.name,
            'size': magnet_info.size,
            'date': magnet_info.date,
            'seeders': magnet_info.seeders,
            'leechers': magnet_info.leechers,
            'downloads': magnet_info.downloads,
            'resolution': magnet_info.resolution,
            'codec': magnet_info.codec,
            'file_list': magnet_info.file_list,
            'submitter': magnet_info.submitter,
            'completed': magnet_info.completed
        }
        print(json.dumps(magnet_dict, ensure_ascii=False, indent=2))

    else:
        print(f"❌ 未找到番号 {TARGET_CODE} 对应的磁力链接")
        print("可能的原因:")
        print("- 番号不存在或拼写错误")
        print("- Sukebei上没有该资源")
        print("- 网络连接问题")


if __name__ == "__main__":
    main()